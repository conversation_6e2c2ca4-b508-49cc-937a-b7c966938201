// src/components/VPSAdminChat.tsx

'use client';

import React, { useEffect, useRef, useCallback, useState } from 'react';
import { AlertTriangle, CheckCircle, XCircle, Info, HelpCircle, Sparkles } from 'lucide-react';

// Import existing hooks and components
import { useMessageHandler } from '@/hooks/useMessageHandler';
import { useTaskManager } from '@/hooks/useTaskManager';
import { useCommandHistory } from '@/hooks/useCommandHistory';
import { useStats } from '@/hooks/useStats';
import { useWebSocket } from '@/hooks/useWebSocket';

// Import components
import ChatHeader from '@/components/ChatHeader';
import MessageList from '@/components/MessageList';
import ChatInput from '@/components/ChatInput';
import SettingsPanel from '@/components/SettingsPanel';
import ProgressIndicator from '@/components/ProgressIndicator';
import MarkdownRenderer from '@/components/MarkdownRenderer';
import BackendStatus from '@/components/BackendStatus';
import TerminalOutputWithModal from '@/components/TerminalOutputWithModal';
import ConfirmationModal from '@/components/ConfirmationModal';
import LeftSidebar from '@/components/LeftSidebar';
import { useAuth } from '@/components/AuthProvider';
// Import services
import { apiService } from '@/services/apiService';

// Import types and constants
import { Message, SSHInfo, SSEEventData, Server } from '@/types';

// Import utilities
import { getInputPlaceholder } from '@/utils/messageRendering';
import { exportTaskHistory } from '@/utils';

// Custom hook for UI state management
import { useUIState } from '@/hooks/useUIState';

const VPSAdminChat: React.FC = () => {
  // Authentication context
  const { token, loading: authLoading, isAuthenticated, logout } = useAuth();

  // Initialize hooks
  const { messages, addMessage, setMessages } = useMessageHandler();
  const { taskId, isTaskActive, setTaskId, setIsTaskActive, currentTask, taskHistory, createTask, setCurrentTask } = useTaskManager();
  const { commandHistory, historyIndex } = useCommandHistory();
  const { executionStats, updateStats } = useStats();

  // UI State management
  const {
    isWaiting,
    showConfirmation,
    commandToConfirm,
    error,
    isAwaitingAnswer,
    showSettingsPanel,
    autoScroll,
    setIsWaiting,
    setShowConfirmation,
    setCommandToConfirm,
    setError,
    setIsAwaitingAnswer,
    setShowSettingsPanel,
    setAutoScroll
  } = useUIState();

  // Input state
  const [inputMessage, setInputMessage] = useState<string>('');

  // Backend connection state
  const [showBackendStatus, setShowBackendStatus] = useState<boolean>(false);
  const [backendConnectionError, setBackendConnectionError] = useState<boolean>(false);

  // Logout confirmation state
  const [showLogoutConfirmation, setShowLogoutConfirmation] = useState<boolean>(false);

  // Left sidebar state
  const [showLeftSidebar, setShowLeftSidebar] = useState<boolean>(false);
  const [currentServerId, setCurrentServerId] = useState<string>('server-1');

  // Dummy server data
  const [servers] = useState<Server[]>([
    {
      id: 'server-1',
      name: 'Production Server',
      host: '*************',
      status: 'online',
      lastConnected: new Date(Date.now() - 1000 * 60 * 5) // 5 minutes ago
    },
    {
      id: 'server-2',
      name: 'Development Server',
      host: '*************',
      status: 'offline',
      lastConnected: new Date(Date.now() - 1000 * 60 * 60 * 2) // 2 hours ago
    },
    {
      id: 'server-3',
      name: 'Staging Server',
      host: '*************',
      status: 'connecting',
      lastConnected: new Date(Date.now() - 1000 * 60 * 30) // 30 minutes ago
    },
    {
      id: 'server-4',
      name: 'Database Server',
      host: '*************',
      status: 'online',
      lastConnected: new Date(Date.now() - 1000 * 60 * 10) // 10 minutes ago
    }
  ]);

  // Enhanced dummy task history data
  const [dummyTaskHistory] = useState(() => [
    {
      id: 'task-1',
      title: 'System Update',
      description: 'Update all packages and restart services',
      status: 'completed' as const,
      progress: 100,
      steps: [],
      startTime: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
      endTime: new Date(Date.now() - 1000 * 60 * 60 * 1.5), // 1.5 hours ago
      totalCommands: 8,
      successfulCommands: 8,
      failedCommands: 0
    },
    {
      id: 'task-2',
      title: 'Database Backup',
      description: 'Create backup of production database',
      status: 'completed' as const,
      progress: 100,
      steps: [],
      startTime: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 hours ago
      endTime: new Date(Date.now() - 1000 * 60 * 60 * 5.5), // 5.5 hours ago
      totalCommands: 3,
      successfulCommands: 3,
      failedCommands: 0
    },
    {
      id: 'task-3',
      title: 'Log Analysis',
      description: 'Analyze error logs from last week',
      status: 'failed' as const,
      progress: 45,
      steps: [],
      startTime: new Date(Date.now() - 1000 * 60 * 60 * 12), // 12 hours ago
      endTime: new Date(Date.now() - 1000 * 60 * 60 * 11.5), // 11.5 hours ago
      totalCommands: 5,
      successfulCommands: 2,
      failedCommands: 3
    },
    {
      id: 'task-4',
      title: 'Security Scan',
      description: 'Run security vulnerability scan',
      status: 'running' as const,
      progress: 75,
      steps: [],
      startTime: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
      totalCommands: 4,
      successfulCommands: 3,
      failedCommands: 0
    }
  ]);

  // Refs
  const abortControllerRef = useRef<AbortController | null>(null);

  // Set authentication token in API service when token changes
  useEffect(() => {
    if (token) {
      apiService.setToken(token);
      console.log("Authentication token set in API service");
    }
  }, [token]);

  // Set initial sidebar state based on screen size
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setShowLeftSidebar(true);
      } else {
        setShowLeftSidebar(false);
      }
    };

    // Set initial state
    handleResize();

    // Add resize listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Cleanup: Abort any ongoing request on component unmount
  useEffect(() => {
    return () => {
      console.log("VPSAdminChat unmounting, aborting any active stream.");
      abortControllerRef.current?.abort();
    };
  }, []);

  // Enhanced addMessage wrapper to handle UI state
  const handleAddMessage = useCallback((
    sender: Message['sender'],
    type: Message['type'],
    content: React.ReactNode,
    rawContent?: string,
    sshInfo?: SSHInfo,
    formData?: any
  ) => {
    // Set isAwaitingAnswer true ONLY if the new message is a question
    setIsAwaitingAnswer(type === 'question');
    // Ensure showConfirmation is false if adding any message other than command_request or interactive_command_request
    if(type !== 'command_request' && type !== 'interactive_command_request') {
        setShowConfirmation(false);
        setCommandToConfirm(null);
    }

    // Use the hook's addMessage function
    addMessage(sender, type, content, rawContent, sshInfo, undefined);

    // If it's a form request, we need to manually add the formData
    if (type === 'form_request' && formData) {
      setMessages((prev: Message[]) => {
        const updated = [...prev];
        const lastMessage = updated[updated.length - 1];
        if (lastMessage) {
          lastMessage.formData = formData;
        }
        return updated;
      });
    }
   }, [addMessage, setIsAwaitingAnswer, setShowConfirmation, setCommandToConfirm, setMessages]);

  // WebSocket message handlers
  const handleStreamMessage = useCallback((message: any) => {
    console.log("DEBUG: WebSocket message received:", message);

    // Filter out heartbeat and system messages that shouldn't be displayed
    if (message.type === 'heartbeat') {
      return; // Don't process heartbeat messages
    }

    // Filter out JSON response objects that might leak through
    if (typeof message.content === 'object' && message.content !== null) {
      // Check if this looks like a WebSocketResponse object
      if ('success' in message.content && 'message' in message.content && 'error_code' in message.content) {
        console.log("DEBUG: Filtering out WebSocketResponse object:", message.content);
        return; // Don't display raw WebSocketResponse objects
      }
    }

    // Convert WebSocket message to SSE format for compatibility
    const data: SSEEventData = {
      type: message.type,
      content: message.content,
      metadata: message.metadata
    };

    // Process different message types from backend
    if (data.type === 'error') {
      handleAddMessage('system', 'error', (
        <>
          <AlertTriangle size={14} className="inline mr-1 text-yellow-500" />
          <MarkdownRenderer content={data.content} />
        </>
      ));
    } else if (data.type === 'question') {
      handleAddMessage('ai', 'question', (
        <>
          <HelpCircle size={14} className="inline mr-1 text-accent-tertiary" />
          <MarkdownRenderer content={data.content} />
        </>
      ));
      setIsWaiting(false);
    } else if (data.type === 'command_confirmation') {
      handleAddMessage('ai', 'command_request', (
        <div className='w-full'>
          <p className="mb-2 font-medium">🤖 AI proposes the following command:</p>
          <div
            className="terminal-command"
            style={{
              backgroundColor: 'rgb(15, 23, 42)',
              color: 'rgb(226, 232, 240)',
              fontFamily: 'Consolas, Monaco, "Courier New", monospace',
              padding: '0.75rem',
              borderRadius: '0.375rem',
              border: '1px solid rgb(71, 85, 105)',
              position: 'relative',
              whiteSpace: 'pre-wrap',
              wordWrap: 'break-word'
            }}
          >
            {data.content}
          </div>
        </div>
      ), data.content);
      setCommandToConfirm(data.content);
      setShowConfirmation(true);
      setIsWaiting(false);
    } else if (data.type === 'interactive_command_confirmation') {
      handleAddMessage('ai', 'interactive_command_request', (
        <div className='w-full'>
          <p className="mb-2 font-medium">🤖 AI proposes the following interactive command:</p>
          <div
            className="terminal-command"
            style={{
              backgroundColor: 'rgb(15, 23, 42)',
              color: 'rgb(226, 232, 240)',
              fontFamily: 'Consolas, Monaco, "Courier New", monospace',
              padding: '0.75rem',
              borderRadius: '0.375rem',
              border: '1px solid rgb(71, 85, 105)',
              position: 'relative',
              whiteSpace: 'pre-wrap',
              wordWrap: 'break-word'
            }}
          >
            {data.content}
          </div>
        </div>
      ), data.content);
      setCommandToConfirm(data.content);
      setShowConfirmation(true);
      setIsWaiting(false);
    } else if (data.type === 'step_skip_option') {
      // Handle step skip option - show advanced confirmation with skip/retry/abort options
      handleAddMessage('ai', 'command_request', (
        <div className='w-full'>
          <p className="mb-2 font-medium text-accent-warning">⚠️ Step has failed multiple times:</p>
          <div className="text-sm text-theme-secondary mb-2">{data.content}</div>
          <div className="text-xs text-theme-tertiary">Choose an option below to continue.</div>
        </div>
      ), data.content);
      setCommandToConfirm(data.content);
      setShowConfirmation(true);
      setIsWaiting(false);
    } else if (data.type === 'task_complete' || data.type === 'task_end') {
      handleAddMessage('system', 'task_end', <><CheckCircle size={14} className="inline mr-1 text-green-500" />{data.content}</>);
      setIsWaiting(false);
      setShowConfirmation(false);
      setCommandToConfirm(null);
      setIsTaskActive(false);
      setIsAwaitingAnswer(false);
    } else if (data.type === 'ssh_output') {
      const sshInfo = data.content as SSHInfo;
      handleAddMessage('system', 'command_output', (
        <TerminalOutputWithModal sshInfo={sshInfo} />
      ), undefined, sshInfo);

      // Update stats
      updateStats(sshInfo?.success ? 'command_success' : 'command_failure', sshInfo?.execution_time);
    } else {
      // Handle other message types
      if (data.type === 'info') {
        handleAddMessage('system', 'info', (
          <>
            <Info size={14} className="inline mr-1 text-blue-500" />
            <MarkdownRenderer content={data.content} />
          </>
        ));
      } else if (data.type === 'warning') {
        handleAddMessage('system', 'warning', (
          <>
            <AlertTriangle size={14} className="inline mr-1 text-yellow-500" />
            <MarkdownRenderer content={data.content} />
          </>
        ));
      } else if (data.type === 'summary') {
        handleAddMessage('ai', 'summary', (
          <>
            <Sparkles size={14} className="inline mr-1 text-accent-tertiary" />
            <MarkdownRenderer content={data.content} />
          </>
        ));
        setIsWaiting(false); // Summary usually indicates end of processing
      } else if (data.type === 'ai_response') {
        // Handle AI responses with markdown rendering
        handleAddMessage('ai', 'ai_response', <MarkdownRenderer content={data.content} />, data.content);
        setIsWaiting(false); // AI response indicates ready for more input
      } else if (data.type === 'progress') {
        // Handle orchestrator progress messages
        handleAddMessage('system', 'progress', (
          <>
            <Info size={14} className="inline mr-1 text-blue-500" />
            <MarkdownRenderer content={data.content} />
          </>
        ));
      } else if (data.type === 'step_start') {
        // Handle step start messages
        handleAddMessage('system', 'step_start', (
          <>
            <Info size={14} className="inline mr-1 text-blue-500" />
            <MarkdownRenderer content={data.content} />
          </>
        ));
      } else if (data.type === 'step_completed' || data.type === 'step_complete') {
        // Handle step completion messages
        handleAddMessage('system', 'step_complete', (
          <>
            <CheckCircle size={14} className="inline mr-1 text-green-500" />
            <MarkdownRenderer content={data.content} />
          </>
        ));
      } else if (data.type === 'command_result') {
        // Handle command execution results from orchestrator
        const metadata = data.metadata;
        // Cast metadata to SSHInfo since it contains the same structure
        const sshInfo = metadata as SSHInfo;
        handleAddMessage('system', 'command_output', (
          <TerminalOutputWithModal sshInfo={sshInfo} />
        ), undefined, sshInfo);

        // Update stats
        updateStats(sshInfo?.success ? 'command_success' : 'command_failure', sshInfo?.execution_time);
      } else if (data.type === 'command_failed') {
        // Handle command failure messages
        handleAddMessage('system', 'error', (
          <>
            <XCircle size={14} className="inline mr-1 text-accent-error" />
            <MarkdownRenderer content={data.content} />
          </>
        ));
      } else if (data.type === 'task_complete') {
        // Handle task completion
        handleAddMessage('system', 'task_complete', (
          <>
            <CheckCircle size={14} className="inline mr-1 text-green-500" />
            <MarkdownRenderer content={data.content} />
          </>
        ));
        setIsWaiting(false);
        setIsTaskActive(false);
      } else if (data.type === 'task_failed') {
        // Handle task failure
        handleAddMessage('system', 'error', (
          <>
            <XCircle size={14} className="inline mr-1 text-accent-error" />
            <MarkdownRenderer content={data.content} />
          </>
        ));
        setIsWaiting(false);
        setIsTaskActive(false);
      } else if (data.type === 'task_aborted') {
        // Handle task abortion
        handleAddMessage('system', 'warning', (
          <>
            <AlertTriangle size={14} className="inline mr-1 text-yellow-500" />
            <MarkdownRenderer content={data.content} />
          </>
        ));
        setIsWaiting(false);
        setIsTaskActive(false);
      } else if (data.type === 'error_recovery_start') {
        // Handle error recovery start
        handleAddMessage('system', 'info', (
          <>
            <AlertTriangle size={14} className="inline mr-1 text-yellow-500" />
            <MarkdownRenderer content={data.content} />
          </>
        ));
      } else if (data.type === 'error_recovery_plan') {
        // Handle error recovery plan creation
        const metadata = data.metadata || {};
        handleAddMessage('system', 'info', (
          <div>
            <AlertTriangle size={14} className="inline mr-1 text-yellow-500" />
            <MarkdownRenderer content={data.content} />
            {metadata.recoverySteps && (
              <div className="mt-2 text-sm text-theme-secondary">
                Recovery approach: {metadata.recoveryApproach || 'Unknown'}
              </div>
            )}
          </div>
        ));
      } else if (data.type === 'recovery_step_start') {
        // Handle recovery step start
        const metadata = data.metadata || {};
        handleAddMessage('system', 'info', (
          <div>
            <AlertTriangle size={14} className="inline mr-1 text-yellow-500" />
            <MarkdownRenderer content={data.content} />
            {metadata.recoveryStepNumber && metadata.totalRecoverySteps && (
              <div className="mt-1 text-xs text-theme-tertiary">
                Recovery step {metadata.recoveryStepNumber} of {metadata.totalRecoverySteps}
              </div>
            )}
          </div>
        ));
      } else if (data.type === 'error_recovery_failed') {
        // Handle error recovery failure
        handleAddMessage('system', 'error', (
          <>
            <XCircle size={14} className="inline mr-1 text-accent-error" />
            <MarkdownRenderer content={data.content} />
          </>
        ));
      } else if (data.type === 'form_request') {
        // Handle form request messages - let FormPrompt handle title and description
        const formRequest = data.content;
        handleAddMessage('ai', 'form_request', null, undefined, undefined, formRequest);
        setIsWaiting(false);
      } else {
        // Handle unknown message types - log and display as info
        console.log('Unknown message type received:', data.type, data);
        handleAddMessage('system', 'info', (
          <>
            <Info size={14} className="inline mr-1 text-blue-500" />
            <MarkdownRenderer content={data.content || `Unknown message type: ${data.type}`} />
          </>
        ));
        // Don't leave the chat in waiting state for unknown messages
        setIsWaiting(false);
      }
    }
  }, [handleAddMessage, setIsWaiting, setShowConfirmation, setCommandToConfirm, setIsTaskActive, setIsAwaitingAnswer, updateStats]);

  const handleStreamError = useCallback((error: Error) => {
    console.error("Stream error:", error);
    setError(error.message);

    // Check if this is a backend connection error
    const isConnectionError = error.message.includes('Cannot connect to backend') ||
                             error.message.includes('Failed to fetch') ||
                             error.message.includes('Backend server is not responding') ||
                             error.message.includes('Backend server error') ||
                             error.message.includes('Connection Error') ||
                             error.message.includes('WebSocket connection failed') ||
                             error.message.includes('WebSocket connection error') ||
                             error.message.includes('Connection failed');

    if (isConnectionError) {
      setBackendConnectionError(true);
      setShowBackendStatus(true);
      // Connection error messages removed - only update backend status
    } else {
      // Other error messages still shown in chat
      handleAddMessage('system', 'error', error.message);
    }

    setIsWaiting(false);
    setShowConfirmation(false);
    setIsAwaitingAnswer(false);
    setIsTaskActive(false);
  }, [handleAddMessage, setError, setIsWaiting, setShowConfirmation, setIsAwaitingAnswer, setIsTaskActive, setBackendConnectionError, setShowBackendStatus]);

  // Initialize WebSocket connection
  const {
    isConnected: wsConnected,
    sendTaskMessage
  } = useWebSocket({
    token: token || 'demo-token', // Use demo-token as fallback for development
    autoConnect: !authLoading && (isAuthenticated || !!token), // Enable auto-connect after auth is loaded
    onMessage: handleStreamMessage,
    onError: handleStreamError,
    onConnect: () => {
      console.log("WebSocket connected successfully");
      setBackendConnectionError(false);
      setError(null);
    },
    onDisconnect: () => {
      console.log("WebSocket disconnected");
      setBackendConnectionError(true);
      setError(null); // Clear any existing error messages
    }
  });

  // Note: Auto-connect is now handled by the useWebSocket hook to avoid race conditions

  // Core function to handle WebSocket communication with the backend
  const handleStream = useCallback((currentTaskId: string, messageToSend: string) => {
    if (!currentTaskId) {
        console.error("handleStream called without task ID.");
        return;
    }

    if (!wsConnected) {
        console.error("WebSocket is not connected.");
        // Connection error messages removed - chat input is already disabled
        return;
    }

    setError(null);
    setIsWaiting(true);
    setShowConfirmation(false);
    setCommandToConfirm(null);

    console.log(`DEBUG: Sending WebSocket message for task ${currentTaskId}: ${messageToSend}`);

    // Send message via WebSocket
    try {
      sendTaskMessage(currentTaskId, messageToSend);
    } catch (error) {
      console.error("Failed to send WebSocket message:", error);
      setError("Failed to send message. Please try again.");
      setIsWaiting(false);
    }

  }, [wsConnected, sendTaskMessage, setError, setIsWaiting, setShowConfirmation, setCommandToConfirm]);


  // --- Start a new task ---
  const startTask = useCallback(async (prompt: string) => {
    const trimmedPrompt = prompt.trim();

    // Allow empty prompts for blank tasks - use placeholder
    const actualPrompt = trimmedPrompt || "New Task";
    const isBlankTask = !trimmedPrompt;

    console.log("INFO: Starting new task...", { isBlankTask, actualPrompt });
    setIsWaiting(true); // Start waiting immediately
    setError(null);
    setMessages([]); // Clear previous messages
    setShowConfirmation(false);
    setCommandToConfirm(null);
    setIsTaskActive(false); // Ensure inactive before start
    setIsAwaitingAnswer(false);
    abortControllerRef.current?.abort(); // Abort any previous stream

    // Add user prompt to UI first (only if not blank)
    if (!isBlankTask) {
      handleAddMessage('user', 'text', <MarkdownRenderer content={trimmedPrompt} />, trimmedPrompt);
    }
    setInputMessage(''); // Clear input immediately

    try {
      // Use the API service to create the task
      const data = await apiService.createTask({ initial_prompt: actualPrompt });
      const newTaskId = data.task_id;

      // Create task in task manager
      createTask(newTaskId, actualPrompt);
      setTaskId(newTaskId);
      setIsTaskActive(true);

      console.log(`INFO: Task ${newTaskId} started successfully.`);

      // For blank tasks, send a special message to prompt for user input
      // For regular tasks, send the initial prompt to kick off AI processing
      const messageToSend = isBlankTask ? "What would you like me to help you with?" : trimmedPrompt;
      handleStream(newTaskId, messageToSend);

    } catch (err: any) {
      console.error("ERROR: Failed to start task:", err);
      const errorMsg = `Failed to start task: ${err.message}`;
      setError(errorMsg);

      // Check if this is a backend connection error
      const isConnectionError = err.message.includes('Cannot connect to backend') ||
                               err.message.includes('Failed to fetch') ||
                               err.message.includes('Backend server is not responding') ||
                               err.message.includes('Backend server error') ||
                               err.message.includes('WebSocket connection failed') ||
                               err.message.includes('WebSocket connection error') ||
                               err.message.includes('Connection failed');

      if (isConnectionError) {
        setBackendConnectionError(true);
        setShowBackendStatus(true);
        // Connection error messages removed - only update backend status
      } else {
        handleAddMessage('system', 'error', errorMsg);
      }

      setIsWaiting(false); // Stop waiting on error
      setIsTaskActive(false); // Remain inactive
    }
  }, [handleAddMessage, setIsWaiting, setError, setIsTaskActive, setIsAwaitingAnswer, setShowConfirmation, setCommandToConfirm, setTaskId, handleStream, createTask, setInputMessage, setMessages]);

  // --- Send a message within an active task ---
  const handleSendMessage = useCallback((message: string) => {
    const trimmedMessage = message.trim();
    // Can send if task is active AND we are NOT waiting for stream AND NOT showing confirmation buttons
    const canSend = isTaskActive && !isWaiting && !showConfirmation;
    if (!trimmedMessage || !taskId || !canSend) {
        console.log("WARN: Send message blocked:", {isTaskActive, isWaiting, showConfirmation, taskId, message: trimmedMessage});
        return;
    }

    console.log(`INFO: Sending message for task ${taskId}: ${trimmedMessage}`);
    handleAddMessage('user', 'text', <MarkdownRenderer content={trimmedMessage} />, trimmedMessage);
    setInputMessage(''); // Clear input after adding
    handleStream(taskId, trimmedMessage); // Send message to backend stream
  }, [isTaskActive, isWaiting, showConfirmation, taskId, handleAddMessage, setInputMessage, handleStream]);

  // --- Handle form submission ---
  const handleFormSubmit = useCallback((formData: Record<string, string>) => {
    if (!taskId || !isTaskActive) {
      console.log("WARN: Form submission blocked:", {isTaskActive, taskId});
      return;
    }

    console.log(`INFO: Handling form submission for task ${taskId}:`, formData);

    // Send the form data to the backend as JSON
    handleStream(taskId, JSON.stringify(formData));
  }, [taskId, isTaskActive, handleAddMessage, handleStream]);

  // --- Handle confirmation (Yes/No/Skip/Retry/Abort/Auto/Manual) ---
  const handleConfirmation = useCallback((confirm: 'yes' | 'no' | 'skip_step' | 'retry_step' | 'abort_task' | 'auto' | 'manual') => {
    // Can confirm if task is active and confirmation is showing
    if (!taskId || !commandToConfirm || !isTaskActive || !showConfirmation) {
        console.log("WARN: Confirmation blocked:", {isTaskActive, showConfirmation, taskId});
        return;
    }

    console.log(`INFO: Handling confirmation '${confirm}' for task ${taskId}`);

    // Hide buttons immediately after click
    setShowConfirmation(false);
    setCommandToConfirm(null);

    // Send the confirmation directly to the backend stream
    handleStream(taskId, confirm);
  }, [taskId, commandToConfirm, isTaskActive, showConfirmation, setShowConfirmation, setCommandToConfirm, handleStream]);

   // Handle Enter key press in input field
   const handleKeyDown = useCallback((event: React.KeyboardEvent<HTMLInputElement>) => {
     if (event.key === 'Enter' && !event.shiftKey) {
       event.preventDefault(); // Prevent newline in input
       // Determine action based on current state
       if (!isTaskActive) { // If no task is active, pressing Enter starts a new task
            startTask(inputMessage);
       } else { // If a task is active...
           // Send message only if NOT waiting AND NOT confirming
           if (!isWaiting && !showConfirmation) {
                handleSendMessage(inputMessage);
           }
           // Otherwise, Enter does nothing (user needs to click Yes/No or wait)
       }
     }
   }, [isTaskActive, inputMessage, isWaiting, showConfirmation, startTask, handleSendMessage]);

  // Logout confirmation handlers
  const handleLogoutRequest = useCallback(() => {
    setShowLogoutConfirmation(true);
  }, []);

  const handleLogoutConfirm = useCallback(() => {
    setShowLogoutConfirmation(false);
    logout();
  }, [logout]);

  const handleLogoutCancel = useCallback(() => {
    setShowLogoutConfirmation(false);
  }, []);

  // Left sidebar handlers
  const handleToggleLeftSidebar = useCallback(() => {
    setShowLeftSidebar(!showLeftSidebar);
  }, [showLeftSidebar]);

  const handleServerSelect = useCallback((serverId: string) => {
    setCurrentServerId(serverId);
    console.log('Selected server:', serverId);
    // Close sidebar on mobile after selection
    if (window.innerWidth < 1024) {
      setShowLeftSidebar(false);
    }
  }, []);

  const handleAddServer = useCallback(() => {
    console.log('Add server requested');
    // TODO: Implement add server functionality
  }, []);

  const handleNewTask = useCallback(() => {
    console.log('New task requested');
    // Create a new task with default values
    const newTask = createTask('New Task', 'Enter your task description...');
    setCurrentTask(newTask);
    setTaskId(newTask.id);
    setIsTaskActive(false); // Start as inactive, user needs to provide input
    // Close sidebar on mobile after action
    if (window.innerWidth < 1024) {
      setShowLeftSidebar(false);
    }
  }, [createTask, setCurrentTask, setTaskId, setIsTaskActive]);

  const handleTaskSelect = useCallback((taskId: string) => {
    console.log('Selected task:', taskId);
    // TODO: Implement task selection functionality
    // Close sidebar on mobile after selection
    if (window.innerWidth < 1024) {
      setShowLeftSidebar(false);
    }
  }, []);

  // Get dynamic placeholder text
  const placeholder = getInputPlaceholder(isTaskActive, isWaiting, showConfirmation, isAwaitingAnswer);

  // --- Main JSX Render ---
  return (
    <div className="flex h-screen lg:h-[85vh] w-full max-w-7xl gap-0 lg:gap-4 transition-all duration-300 relative">
      {/* Left Sidebar */}
      <LeftSidebar
        isOpen={showLeftSidebar}
        onToggle={handleToggleLeftSidebar}
        servers={servers}
        taskHistory={dummyTaskHistory}
        currentServerId={currentServerId}
        onServerSelect={handleServerSelect}
        onAddServer={handleAddServer}
        onNewTask={handleNewTask}
        onTaskSelect={handleTaskSelect}
      />

      {/* Main Chat Container */}
      <div className="flex flex-col flex-1 bg-theme-primary rounded-none lg:rounded-xl shadow-none lg:shadow-2xl border-0 lg:border border-theme-primary overflow-hidden min-h-0 transition-all duration-300">
        {/* Header */}
        <ChatHeader
          isTaskActive={isTaskActive}
          taskId={taskId}
          showSettingsPanel={showSettingsPanel}
          onToggleSettings={() => setShowSettingsPanel(!showSettingsPanel)}
          showLeftSidebar={showLeftSidebar}
          onToggleLeftSidebar={handleToggleLeftSidebar}
        />

        {/* Progress Indicator */}
        {currentTask && (
          <ProgressIndicator
            task={currentTask}
            className="px-3 py-2 border-b border-theme-primary transition-all duration-300"
          />
        )}

        {/* Message List */}
        <MessageList
          messages={messages}
          showConfirmation={showConfirmation}
          commandToConfirm={commandToConfirm}
          isTaskActive={isTaskActive}
          isWaiting={isWaiting}
          isAwaitingAnswer={isAwaitingAnswer}
          autoScroll={autoScroll}
          onConfirmation={handleConfirmation}
          onFormSubmit={handleFormSubmit}
        />

        {/* Input Area */}
        <div className="p-3 border-t border-theme-primary bg-theme-primary flex-shrink-0 transition-all duration-300">
          {/* Display persistent error messages */}
          {error && !backendConnectionError && <p className="text-accent-error text-xs mb-1.5 text-center">{error}</p>}

          {/* Backend Status Section */}
          {backendConnectionError && (
            <div className="mb-3 space-y-2">
              <div className="flex items-center justify-between">
                <p className="text-accent-error text-xs">{error}</p>
                <button
                  onClick={() => setShowBackendStatus(!showBackendStatus)}
                  className="text-xs bg-accent-primary text-white px-2 py-1 rounded-lg hover:opacity-90 transition-all duration-200"
                >
                  {showBackendStatus ? 'Hide' : 'Show'} Backend Status
                </button>
              </div>

              {showBackendStatus && (
                <div className="max-h-64 overflow-y-auto">
                  <BackendStatus
                    onStatusChange={(isOnline) => {
                      if (isOnline) {
                        setBackendConnectionError(false);
                        setShowBackendStatus(false);
                        setError(null);
                      }
                    }}
                    autoRefresh={true}
                    refreshInterval={10000} // Check every 10 seconds when shown
                  />
                </div>
              )}
            </div>
          )}

          <ChatInput
            value={inputMessage}
            onChange={setInputMessage}
            onSubmit={() => {
              if (!isTaskActive) {
                startTask(inputMessage);
              } else if (isTaskActive && !isWaiting && !showConfirmation) {
                handleSendMessage(inputMessage);
              }
            }}
            onKeyDown={handleKeyDown}
            disabled={isWaiting || showConfirmation || !wsConnected}
            placeholder={!wsConnected ? "Connecting to server..." : placeholder}
            commandHistory={commandHistory}
            historyIndex={historyIndex}
          />
        </div>
      </div>

      {/* Settings Panel - Responsive Layout */}
      {showSettingsPanel && (
        <div className={`
          ${/* Desktop: Side panel */ ''}
          lg:relative lg:w-80 lg:flex-shrink-0
          ${/* Mobile: Full screen overlay */ ''}
          fixed lg:static inset-0 lg:inset-auto lg:top-0 lg:right-0 lg:h-auto
          w-full lg:w-80 h-full lg:h-auto
          z-50 lg:z-auto
          transform lg:transform-none transition-transform duration-300
          ${showSettingsPanel ? 'translate-x-0' : 'translate-x-full lg:translate-x-0'}
        `}>
          <SettingsPanel
            executionStats={executionStats}
            currentTask={currentTask}
            taskHistory={taskHistory}
            messages={messages}
            autoScroll={autoScroll}
            wsConnected={wsConnected}
            onClose={() => setShowSettingsPanel(false)}
            onExportHistory={() => {
              console.log('Export history requested');
              exportTaskHistory(taskHistory, executionStats, messages);
            }}
            onToggleAutoScroll={() => setAutoScroll(!autoScroll)}
            onLogoutRequest={handleLogoutRequest}
          />
        </div>
      )}

      {/* Logout Confirmation Modal */}
      <ConfirmationModal
        isOpen={showLogoutConfirmation}
        onClose={handleLogoutCancel}
        onConfirm={handleLogoutConfirm}
        title="Confirm Logout"
        message="Are you sure you want to logout? You will need to sign in again to access the application."
        confirmText="Logout"
        cancelText="Cancel"
        confirmButtonStyle="danger"
      />
    </div>
  );
};

export default VPSAdminChat;
