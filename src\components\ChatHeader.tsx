/**
 * Enhanced chat header component for VPS Admin Chat
 */

import React from 'react';
import { Spark<PERSON>, Settings, Menu } from 'lucide-react';
import { motion } from 'framer-motion';

interface ChatHeaderProps {
  isTaskActive: boolean;
  taskId: string | null;
  showSettingsPanel?: boolean;
  onToggleSettings?: () => void;
  showLeftSidebar?: boolean;
  onToggleLeftSidebar?: () => void;
  className?: string;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({
  isTaskActive,
  taskId,
  showSettingsPanel = false,
  onToggleSettings,
  showLeftSidebar = false,
  onToggleLeftSidebar,
  className = ""
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`p-3 border-b border-theme-primary bg-theme-primary flex-shrink-0 flex items-center justify-between transition-colors duration-200 ${className}`}
    >
      {/* Left side - Icon and Title */}
      <div className="flex items-center gap-3 flex-1">
        {/* Left Sidebar Toggle Button - Desktop only */}
        {onToggleLeftSidebar && (
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onToggleLeftSidebar}
            className={`hidden lg:flex p-2 rounded-lg transition-colors ${
              showLeftSidebar
                ? 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                : 'bg-theme-secondary text-theme-secondary hover:bg-theme-tertiary'
            }`}
            title="Toggle Sidebar"
          >
            <Menu size={14} />
          </motion.button>
        )}

        <motion.div
          whileHover={{ scale: 1.1, rotate: 5 }}
          whileTap={{ scale: 0.95 }}
        >
          <Sparkles size={20} className="text-teal-500"/>
        </motion.div>
        <div className="flex flex-col">
          <h1 className="text-sm lg:text-md font-semibold text-theme-primary">
            VPS Admin
          </h1>
          <span className="text-xs text-theme-secondary hidden lg:block">
            AI-Powered Server Management
          </span>
        </div>
      </div>

      {/* Right side - Controls and Status */}
      <div className="flex items-center gap-2">

        {/* Settings Toggle Button */}
        {onToggleSettings && (
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onToggleSettings}
            className={`p-2 rounded-lg transition-colors ${
              showSettingsPanel
                ? 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                : 'bg-theme-secondary text-theme-secondary hover:bg-theme-tertiary'
            }`}
            title="Toggle Settings Panel"
          >
            <Settings size={14} />
          </motion.button>
        )}

        {/* Status Indicator */}
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          className={`text-xs font-medium px-3 py-1.5 rounded-full transition-all duration-200 ${
            isTaskActive
              ? 'bg-green-100 text-green-800 ring-1 ring-green-200'
              : 'bg-theme-secondary text-theme-secondary ring-1 border-theme-primary'
          }`}
        >
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${
              isTaskActive ? 'bg-green-500 animate-pulse' : 'bg-gray-400'
            }`} />
            <span>
              {isTaskActive ? 'Active' : 'Inactive'}
              {taskId && (
                <span className="ml-1 opacity-75">
                  ({taskId.substring(0,6)})
                </span>
              )}
            </span>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default ChatHeader;
