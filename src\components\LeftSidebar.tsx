/**
 * LeftSidebar component containing server panel and task history
 */

import React from 'react';
import { X, Menu } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { LeftSidebarProps } from '@/types';
import ServerPanel from './ServerPanel';
import TaskHistoryPanel from './TaskHistoryPanel';

const LeftSidebar: React.FC<LeftSidebarProps> = ({
  isOpen,
  onToggle,
  servers,
  taskHistory,
  currentServerId,
  onServerSelect,
  onAddServer,
  onNewTask,
  onTaskSelect
}) => {
  return (
    <>
      {/* Mobile Toggle Button */}
      <button
        onClick={onToggle}
        className={`lg:hidden fixed top-4 left-4 z-50 p-2 bg-theme-primary border border-theme-primary rounded-lg shadow-lg transition-all duration-300 ${
          isOpen ? 'text-theme-primary' : 'text-theme-secondary'
        }`}
      >
        {isOpen ? <X size={20} /> : <Menu size={20} />}
      </button>

      {/* Mobile Backdrop */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onToggle}
            className="lg:hidden fixed inset-0 bg-black/50 z-40"
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <motion.div
        initial={false}
        animate={{
          x: isOpen ? 0 : '-100%',
        }}
        transition={{
          type: 'spring',
          stiffness: 300,
          damping: 30,
        }}
        className={`
          fixed lg:relative
          top-0 left-0
          h-full lg:h-auto
          w-80 lg:w-80
          bg-theme-primary
          border-r border-theme-primary
          shadow-xl lg:shadow-2xl
          z-50 lg:z-auto
          flex flex-col
          lg:flex
          ${isOpen ? 'flex' : 'hidden lg:flex'}
        `}
      >
        {/* Sidebar Header */}
        <div className="p-4 border-b border-theme-primary flex items-center justify-between lg:justify-center">
          <h2 className="text-lg font-semibold text-theme-primary">VPS Admin</h2>
          <button
            onClick={onToggle}
            className="lg:hidden p-1 text-theme-tertiary hover:text-theme-primary transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* Sidebar Content */}
        <div className="flex-1 overflow-y-auto p-4 space-y-6">
          {/* Server Panel */}
          <ServerPanel
            servers={servers}
            currentServerId={currentServerId}
            onServerSelect={onServerSelect}
            onAddServer={onAddServer}
          />

          {/* Divider */}
          <div className="border-t border-theme-primary" />

          {/* Task History Panel */}
          <TaskHistoryPanel
            tasks={taskHistory}
            onTaskSelect={onTaskSelect}
            onNewTask={onNewTask}
          />
        </div>

        {/* Sidebar Footer */}
        <div className="p-4 border-t border-theme-primary">
          <div className="text-xs text-theme-tertiary text-center">
            {servers.filter(s => s.status === 'online').length} of {servers.length} servers online
          </div>
        </div>
      </motion.div>
    </>
  );
};

export default LeftSidebar;
