/**
 * ServerPanel component for server switching and management
 */

import React from 'react';
import { Server, Plus, Wifi, WifiOff, Loader } from 'lucide-react';
import { motion } from 'framer-motion';
import { ServerPanelProps } from '@/types';

const ServerPanel: React.FC<ServerPanelProps> = ({
  servers,
  currentServerId,
  onServerSelect,
  onAddServer
}) => {
  const getStatusIcon = (status: 'online' | 'offline' | 'connecting') => {
    switch (status) {
      case 'online':
        return <Wifi size={14} className="text-green-500" />;
      case 'offline':
        return <WifiOff size={14} className="text-red-500" />;
      case 'connecting':
        return <Loader size={14} className="text-yellow-500 animate-spin" />;
      default:
        return <WifiOff size={14} className="text-gray-500" />;
    }
  };

  const getStatusColor = (status: 'online' | 'offline' | 'connecting') => {
    switch (status) {
      case 'online':
        return 'border-green-500 bg-green-50 dark:bg-green-900/20';
      case 'offline':
        return 'border-red-500 bg-red-50 dark:bg-red-900/20';
      case 'connecting':
        return 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20';
      default:
        return 'border-gray-500 bg-gray-50 dark:bg-gray-900/20';
    }
  };

  return (
    <div className="space-y-3">
      {/* Panel Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-semibold text-theme-primary flex items-center gap-2">
          <Server size={16} className="text-accent-primary" />
          Servers
        </h3>
      </div>

      {/* Server List */}
      <div className="space-y-2">
        {servers.map((server) => (
          <motion.button
            key={server.id}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => onServerSelect(server.id)}
            className={`
              w-full p-3 rounded-lg border-2 transition-all duration-200 text-left
              ${currentServerId === server.id
                ? 'border-accent-primary bg-accent-primary/10 shadow-md'
                : `${getStatusColor(server.status)} hover:shadow-md`
              }
            `}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3 min-w-0 flex-1">
                {/* Server Icon */}
                <div className="flex-shrink-0 w-8 h-8 rounded-full bg-theme-secondary flex items-center justify-center">
                  <Server size={16} className="text-theme-primary" />
                </div>
                
                {/* Server Info */}
                <div className="min-w-0 flex-1">
                  <div className="font-medium text-theme-primary text-sm truncate">
                    {server.name}
                  </div>
                  <div className="text-xs text-theme-tertiary truncate">
                    {server.host}
                  </div>
                </div>
              </div>

              {/* Status Icon */}
              <div className="flex-shrink-0">
                {getStatusIcon(server.status)}
              </div>
            </div>
          </motion.button>
        ))}
      </div>

      {/* Add Server Button */}
      <motion.button
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        onClick={onAddServer}
        className="w-full p-3 border-2 border-dashed border-theme-tertiary hover:border-accent-primary rounded-lg transition-all duration-200 group"
      >
        <div className="flex items-center justify-center gap-2 text-theme-tertiary group-hover:text-accent-primary">
          <Plus size={16} />
          <span className="text-sm font-medium">Add Server</span>
        </div>
      </motion.button>
    </div>
  );
};

export default ServerPanel;
