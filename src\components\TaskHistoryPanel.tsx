/**
 * TaskHistoryPanel component for task history and management
 */

import React from 'react';
import { History, Plus, Clock, CheckCircle, XCircle, Pause, Play } from 'lucide-react';
import { motion } from 'framer-motion';
import { TaskHistoryPanelProps } from '@/types';

const TaskHistoryPanel: React.FC<TaskHistoryPanelProps> = ({
  tasks,
  onTaskSelect,
  onNewTask
}) => {
  const getStatusIcon = (status: 'pending' | 'running' | 'completed' | 'failed' | 'paused') => {
    switch (status) {
      case 'completed':
        return <CheckCircle size={14} className="text-green-500" />;
      case 'failed':
        return <XCircle size={14} className="text-red-500" />;
      case 'running':
        return <Play size={14} className="text-blue-500" />;
      case 'paused':
        return <Pause size={14} className="text-yellow-500" />;
      case 'pending':
        return <Clock size={14} className="text-gray-500" />;
      default:
        return <Clock size={14} className="text-gray-500" />;
    }
  };

  const getStatusColor = (status: 'pending' | 'running' | 'completed' | 'failed' | 'paused') => {
    switch (status) {
      case 'completed':
        return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20';
      case 'failed':
        return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20';
      case 'running':
        return 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20';
      case 'paused':
        return 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/20';
      case 'pending':
        return 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800/20';
      default:
        return 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800/20';
    }
  };

  const formatTime = (date?: Date) => {
    if (!date) return 'Unknown';
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  };

  return (
    <div className="space-y-3">
      {/* Panel Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-semibold text-theme-primary flex items-center gap-2">
          <History size={16} className="text-accent-primary" />
          Task History
        </h3>
      </div>

      {/* Task List */}
      <div className="space-y-2 max-h-64 overflow-y-auto">
        {tasks.length === 0 ? (
          <div className="text-center py-6 text-theme-tertiary">
            <History size={24} className="mx-auto mb-2 opacity-50" />
            <p className="text-sm">No tasks yet</p>
          </div>
        ) : (
          tasks.map((task) => (
            <motion.button
              key={task.id}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => onTaskSelect(task.id)}
              className={`
                w-full p-3 rounded-lg border transition-all duration-200 text-left
                ${getStatusColor(task.status)} hover:shadow-md
              `}
            >
              <div className="space-y-2">
                {/* Task Header */}
                <div className="flex items-start justify-between gap-2">
                  <div className="min-w-0 flex-1">
                    <div className="font-medium text-theme-primary text-sm truncate">
                      {task.title}
                    </div>
                    <div className="text-xs text-theme-tertiary truncate">
                      {task.description}
                    </div>
                  </div>
                  <div className="flex-shrink-0">
                    {getStatusIcon(task.status)}
                  </div>
                </div>

                {/* Task Progress */}
                {task.progress > 0 && (
                  <div className="space-y-1">
                    <div className="flex justify-between text-xs text-theme-tertiary">
                      <span>Progress</span>
                      <span>{task.progress}%</span>
                    </div>
                    <div className="w-full bg-theme-tertiary rounded-full h-1.5">
                      <div
                        className="bg-accent-primary h-1.5 rounded-full transition-all duration-300"
                        style={{ width: `${task.progress}%` }}
                      />
                    </div>
                  </div>
                )}

                {/* Task Metadata */}
                <div className="flex items-center justify-between text-xs text-theme-tertiary">
                  <span>{formatTime(task.startTime)}</span>
                  {task.totalCommands > 0 && (
                    <span>{task.successfulCommands}/{task.totalCommands} commands</span>
                  )}
                </div>
              </div>
            </motion.button>
          ))
        )}
      </div>

      {/* New Task Button */}
      <motion.button
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        onClick={onNewTask}
        className="w-full p-3 border-2 border-dashed border-theme-tertiary hover:border-accent-primary rounded-lg transition-all duration-200 group"
      >
        <div className="flex items-center justify-center gap-2 text-theme-tertiary group-hover:text-accent-primary">
          <Plus size={16} />
          <span className="text-sm font-medium">New Task</span>
        </div>
      </motion.button>
    </div>
  );
};

export default TaskHistoryPanel;
